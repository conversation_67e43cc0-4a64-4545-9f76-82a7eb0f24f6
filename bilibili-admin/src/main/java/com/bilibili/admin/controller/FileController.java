package com.bilibili.admin.controller;

import com.bilibili.common.service.FileService;
import com.bilibili.common.utils.Response;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;

@RestController
@RequestMapping("/file")
@Validated
public class FileController {

    @Resource
    private FileService fileService;

    @PostMapping("/uploadImage")
    public Response<String> uploadImage( @NotNull MultipartFile file, @NotNull Boolean createThumbnail) throws IOException {
        String sourcePath = fileService.uploadImage(file, createThumbnail);
        return Response.success(sourcePath);
    }

    @GetMapping("getResource")
    public void getResource(HttpServletResponse response, @NotNull String sourceName) {
        fileService.getResource(response, sourceName);
    }
}
