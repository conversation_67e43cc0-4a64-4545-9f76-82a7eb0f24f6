package com.bilibili.common.utils;

import com.bilibili.common.config.app.AppConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class FFmpegUtilTest {

    @Mock
    private AppConfig appConfig;

    @InjectMocks
    private FFmpegUtil ffmpegUtil;

    @Test
    void testCreateImageThumbnail_WithValidPath() {
        // 测试正常情况
        String validPath = "/path/to/image.jpg";
        
        // 这个测试主要验证不会抛出 IndexOutOfBoundsException
        // 由于 ProcessUtils.executeCommand 会实际执行命令，我们只测试路径解析部分
        assertDoesNotThrow(() -> {
            // 模拟 FFmpegUtil 中的路径处理逻辑
            int lastDotIndex = validPath.lastIndexOf(".");
            if (lastDotIndex == -1) {
                throw new IllegalArgumentException("文件路径必须包含扩展名: " + validPath);
            }
            String targetPath = validPath.substring(0, lastDotIndex);
            assertEquals("/path/to/image", targetPath);
        });
    }

    @Test
    void testCreateImageThumbnail_WithoutExtension() {
        // 测试没有扩展名的情况
        String invalidPath = "/path/to/image";
        
        assertThrows(IllegalArgumentException.class, () -> {
            ffmpegUtil.createImageThumbnail(invalidPath);
        });
    }

    @Test
    void testCreateImageThumbnail_WithEmptyPath() {
        // 测试空路径
        String emptyPath = "";
        
        assertThrows(IllegalArgumentException.class, () -> {
            ffmpegUtil.createImageThumbnail(emptyPath);
        });
    }

    @Test
    void testCreateImageThumbnail_WithDotAtEnd() {
        // 测试以点结尾的路径
        String pathWithDotAtEnd = "/path/to/image.";
        
        assertDoesNotThrow(() -> {
            // 这种情况应该能正常处理，虽然扩展名为空
            int lastDotIndex = pathWithDotAtEnd.lastIndexOf(".");
            String targetPath = pathWithDotAtEnd.substring(0, lastDotIndex);
            assertEquals("/path/to/image", targetPath);
        });
    }
}
