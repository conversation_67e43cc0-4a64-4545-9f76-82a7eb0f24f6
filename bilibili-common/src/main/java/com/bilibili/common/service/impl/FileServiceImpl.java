package com.bilibili.common.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.bilibili.common.config.app.AppConfig;
import com.bilibili.common.constants.FileConstant;
import com.bilibili.common.constants.IntegerConstant;
import com.bilibili.common.exception.BusinessException;
import com.bilibili.common.model.enums.DateTimePatternEnum;
import com.bilibili.common.service.FileService;
import com.bilibili.common.utils.FFmpegUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static com.bilibili.common.constants.FileConstant.*;

@Service
@Slf4j
public class FileServiceImpl implements FileService {

    @Resource
    private AppConfig appConfig;
    @Resource
    private FFmpegUtil ffmpegUtil;

    // 定义多个安全根目录（必须使用绝对路径）
    private static final List<Path> ALLOWED_ROOTS = Arrays.asList(
            Paths.get(COVER_FILE_PATH).normalize(),
            Paths.get(TEMP_FILE_PATH).normalize(),
            Paths.get(VIDEO_FILE_PATH).normalize()
    );

    @Override
    public String uploadImage(MultipartFile file, Boolean createThumbnail) throws IOException {
        String month = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateTimePatternEnum.YYYY_MM.getPattern()));
        // 图片保存本地完整路径
        String localFullUploadDir  = appConfig.getProjectFolder() + FileConstant.FILE_BASE_PATH + COVER_FILE_PATH + month;
        // 创建目录
        File localFullUploadDirFile = new File(localFullUploadDir);
        if (!localFullUploadDirFile.exists()) {
            localFullUploadDirFile.mkdirs();
        }
        // 图片重命名
        String originalFilename = file.getOriginalFilename();
        Objects.requireNonNull(originalFilename, "文件名不能为空");

        // 安全地获取文件扩展名
        int lastDotIndex = originalFilename.lastIndexOf(".");
        if (lastDotIndex == -1) {
            throw new BusinessException("上传的文件必须包含扩展名");
        }
        String fileSuffix = originalFilename.substring(lastDotIndex);
        String finalFileName = RandomUtil.randomNumbers(IntegerConstant.THIRTY) + fileSuffix;
        // 保存图片到本地
        String filePath = localFullUploadDir + "/" + finalFileName;
        file.transferTo(new File(filePath));
        // 创建缩略图
        if (createThumbnail) {
            ffmpegUtil.createImageThumbnail(filePath);
        }

        return COVER_FILE_PATH + month + "/" + finalFileName;

    }

    @Override
    public void getResource(HttpServletResponse response, String sourceName) {
        // 校验路径是否安全
        validateSourceName(sourceName);
        // 设置响应类型和响应头
        int lastDotIndex = sourceName.lastIndexOf(".");
        if (lastDotIndex == -1) {
            throw new BusinessException("资源文件路径必须包含扩展名: " + sourceName);
        }
        String suffix = sourceName.substring(lastDotIndex);
        response.setContentType("image/" + suffix.replace(".", ""));
        response.setHeader("Cache-Control", "max-age=31536000");
        // 读取文件, 写入响应
        readFile2Response(response, sourceName);
    }

    private void validateSourceName(String sourceName) {
        Path userPath = Paths.get(sourceName).normalize();
        ALLOWED_ROOTS.stream()
                .filter(userPath::startsWith)
                .findFirst()
                .orElseThrow(() -> new BusinessException("非法路径"));
    }

    private void readFile2Response(HttpServletResponse response, String sourceName) {
        String localFullPath = appConfig.getProjectFolder() + FileConstant.FILE_BASE_PATH + sourceName;
        File file = new File(localFullPath);
        if (!file.exists()) {
            return;
        }
        try (OutputStream out =  response.getOutputStream();
             FileInputStream in = new FileInputStream(file)) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = in.read(buffer)) != -1) {
                out.write(buffer, 0, len);
            }
        } catch (Exception e) {
            log.error("文件读写异常{}", e.getMessage());
        }
    }
}
