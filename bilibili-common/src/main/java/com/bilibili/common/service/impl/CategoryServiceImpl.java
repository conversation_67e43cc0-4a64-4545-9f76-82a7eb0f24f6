package com.bilibili.common.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.bilibili.common.component.RedisComponent;
import com.bilibili.common.exception.BusinessException;
import com.bilibili.common.mapper.CategoryMapper;
import com.bilibili.common.model.entity.Category;
import com.bilibili.common.model.vo.LoadCategoryResponse;
import com.bilibili.common.service.CategoryService;
import io.lettuce.core.RedisCommandTimeoutException;
import io.lettuce.core.RedisException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.QueryTimeoutException;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Service
@Slf4j
public class CategoryServiceImpl implements CategoryService {

    @Resource
    private CategoryMapper categoryMapper;
    @Resource
    private RedisComponent redisComponent;

    private static final Integer ROOT_CATEGORY_PID = 0;

    @Override
    public List<LoadCategoryResponse> loadCategory() {
        // 1. 从Redis获取
        List<LoadCategoryResponse> categoryList = null;
        try {
            categoryList = redisComponent.getCategoryList();
        } catch (RedisException | QueryTimeoutException e) {
            log.warn("从Redis获取分类列表失败: {}", e.getMessage());
        }
        if (CollectionUtil.isNotEmpty(categoryList)) {
            return categoryList;
        }

        // 2. 从数据库获取（赋值给等效不可变变量）
        final List<LoadCategoryResponse> finalCategoryList = buildCategoryTree();

        // 3. 异步保存到Redis
        try (var executor = Executors.newVirtualThreadPerTaskExecutor()) {
            executor.submit(() -> {
                try {
                    redisComponent.saveCategoryList(finalCategoryList); // 使用 final 变量
                } catch (RedisException e) {
                    log.error("异步保存分类列表到Redis失败: {}", e.getMessage());
                }
            });
        }

        return finalCategoryList;
    }

    private List<LoadCategoryResponse> buildCategoryTree() {
        List<LoadCategoryResponse> categoryList;
        // 1. 单次查询所有分类（包含一级和二级）
        List<LoadCategoryResponse> allCategoryList = categoryMapper.selectCategoryList();

        // 2. 按父ID分组（避免多次查询数据库）
        Map<Integer, List<LoadCategoryResponse>> childrenMap = allCategoryList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(LoadCategoryResponse::getCategoryPid));
        // 3. 组装一级分类，并设置子分类
        categoryList = allCategoryList.stream()
                .filter(c -> ROOT_CATEGORY_PID.equals(c.getCategoryPid()))
                .peek(parent -> {
                    List<LoadCategoryResponse> child = childrenMap.get(parent.getCategoryId());
                    parent.setChildren(child != null ? child : new ArrayList<>(0));
                })
                .toList();
        return categoryList;
    }

    @Override
    public void saveOrUpdateCategory(Category category) {
        Category oldCategory = categoryMapper.selectCategoryByCategoryCode(category.getCategoryCode());
        // 新增时分类编码重复
        if (category.getCategoryId() == null && oldCategory != null) {
            throw new BusinessException("分类编码已存在");
        }
        // 修改时分类编码重复
        if (category.getCategoryId() != null && oldCategory != null && !category.getCategoryId().equals(oldCategory.getCategoryId())) {
            throw new BusinessException("分类编码已存在");
        }
        if (category.getCategoryId() == null) {
            // 获取一个分类下的最大排序值
            Integer maxSort = categoryMapper.selectMaxSort(category.getCategoryPid());
            category.setSort(maxSort + 1);
            categoryMapper.insert(category);
        } else {
            categoryMapper.updateByCategoryId(category, category.getCategoryId());
        }
        // 刷新缓存
        this.saveCategoryList2Redis();
    }

    @Override
    public void deleteCategory(Integer categoryId) {
        // TODO 校验分类下是否有视频
        // 删除分类及子分类
        categoryMapper.deleteByCategoryId(categoryId);
        // 刷新缓存
        this.saveCategoryList2Redis();
    }


    @Override
    public void changeSort(String categoryIds) {
        if (StrUtil.isBlank(categoryIds)) return;
        List<String> ids = StrUtil.split(categoryIds, ",");
        List<Category> updateCategoryList = IntStream.range(0, ids.size())
                .mapToObj(index -> Category.builder()
                        .categoryId(Integer.parseInt(ids.get(index)))
                        .sort(index + 1)
                        .build())
                .toList();
        categoryMapper.batchUpdateCategorySort(updateCategoryList);

        // 刷新缓存
        this.saveCategoryList2Redis();
    }

    private void saveCategoryList2Redis() {
        List<LoadCategoryResponse> categoryList = this.buildCategoryTree();
        redisComponent.saveCategoryList(categoryList);
    }
}
