package com.bilibili.common.config.web;

import com.bilibili.common.interceptor.LoginInterceptor;
import com.bilibili.common.interceptor.RefreshTokenInterceptor;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Resource
    private RefreshTokenInterceptor refreshTokenInterceptor;
    @Resource
    private LoginInterceptor loginInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册拦截器拦截所有路径
        registry.addInterceptor(refreshTokenInterceptor).addPathPatterns("/web/**").order(0);
        registry.addInterceptor(loginInterceptor).addPathPatterns("/admin/**").order(1);
    }
}
