package com.bilibili.common.utils;

import cn.hutool.core.util.RuntimeUtil;
import com.bilibili.common.config.app.AppConfig;
import jakarta.annotation.Resource;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Component;

@Component
public class FFmpegUtil {

    @Resource
    private AppConfig appConfig;

    public void createImageThumbnail(String sourcePath) {
        String targetPath = sourcePath.substring(0, sourcePath.lastIndexOf(".") - 1);
        String CMD = "ffmpeg -i \"%s\" -vf scale=200:-1 \"%s\"";
        String.format(CMD, sourcePath, targetPath + "_thumbnail.jpg");
        ProcessUtils.executeCommand(CMD, true);
    }
}
