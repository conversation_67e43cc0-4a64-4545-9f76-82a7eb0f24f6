package com.bilibili.common.utils;

import cn.hutool.core.util.RuntimeUtil;
import com.bilibili.common.config.app.AppConfig;
import jakarta.annotation.Resource;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Component;

@Component
public class FFmpegUtil {

    @Resource
    private AppConfig appConfig;

    public void createImageThumbnail(String sourcePath) {
        // 安全地获取文件扩展名位置
        int lastDotIndex = sourcePath.lastIndexOf(".");
        if (lastDotIndex == -1) {
            throw new IllegalArgumentException("文件路径必须包含扩展名: " + sourcePath);
        }

        // 获取不包含扩展名的文件路径
        String targetPath = sourcePath.substring(0, lastDotIndex);
        String CMD = "ffmpeg -i \"%s\" -vf scale=200:-1 \"%s\"";
        // 修复：正确使用 String.format 的返回值
        String formattedCmd = String.format(CMD, sourcePath, targetPath + "_thumbnail.jpg");
        ProcessUtils.executeCommand(formattedCmd, true);
    }
}
