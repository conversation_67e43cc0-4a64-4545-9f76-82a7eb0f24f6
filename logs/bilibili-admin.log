2025-08-05 09:00:41 [INFO][org.hibernate.validator.internal.util.Version][<clinit>][21]-> HV000001: Hibernate Validator 8.0.2.Final
2025-08-05 09:00:41 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStarting][53]-> Starting BilibiliAdminApplication using Java 21.0.7 with PID 15496 (D:\Workspace\java_code\bilibili-backend\bilibili-admin\target\classes started by 26788 in D:\Workspace\java_code\bilibili-backend)
2025-08-05 09:00:41 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStartupProfileInfo][652]-> No active profile set, falling back to 1 default profile: "default"
2025-08-05 09:00:41 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-05 09:00:41 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-08-05 09:00:41 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 12 ms. Found 0 Elasticsearch repository interfaces.
2025-08-05 09:00:41 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-05 09:00:41 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-08-05 09:00:41 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 0 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-08-05 09:00:41 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-05 09:00:41 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-05 09:00:41 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 0 ms. Found 0 Redis repository interfaces.
2025-08-05 09:00:42 [INFO][org.springframework.boot.web.embedded.tomcat.TomcatWebServer][initialize][111]-> Tomcat initialized with port 7070 (http)
2025-08-05 09:00:42 [INFO][org.apache.coyote.http11.Http11NioProtocol][log][173]-> Initializing ProtocolHandler ["http-nio-7070"]
2025-08-05 09:00:42 [INFO][org.apache.catalina.core.StandardService][log][173]-> Starting service [Tomcat]
2025-08-05 09:00:42 [INFO][org.apache.catalina.core.StandardEngine][log][173]-> Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-08-05 09:00:42 [INFO][org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/admin]][log][173]-> Initializing Spring embedded WebApplicationContext
2025-08-05 09:00:42 [INFO][org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext][prepareWebApplicationContext][301]-> Root WebApplicationContext: initialization completed in 1362 ms
2025-08-05 09:00:42 [INFO][com.alibaba.druid.spring.boot3.autoconfigure.DruidDataSourceAutoConfigure][dataSource][66]-> Init DruidDataSource
2025-08-05 09:00:42 [INFO][com.alibaba.druid.pool.DruidDataSource][init][1007]-> {dataSource-1} inited
2025-08-05 09:00:44 [INFO][org.apache.coyote.http11.Http11NioProtocol][log][173]-> Starting ProtocolHandler ["http-nio-7070"]
2025-08-05 09:00:44 [INFO][org.springframework.boot.web.embedded.tomcat.TomcatWebServer][start][243]-> Tomcat started on port 7070 (http) with context path '/admin'
2025-08-05 09:00:44 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStarted][59]-> Started BilibiliAdminApplication in 3.463 seconds (process running for 4.354)
2025-08-05 09:00:59 [INFO][org.springframework.boot.web.embedded.tomcat.GracefulShutdown][shutDownGracefully][54]-> Commencing graceful shutdown. Waiting for active requests to complete
2025-08-05 09:00:59 [INFO][org.springframework.boot.web.embedded.tomcat.GracefulShutdown][doShutdown][76]-> Graceful shutdown complete
2025-08-05 09:00:59 [INFO][com.alibaba.druid.pool.DruidDataSource][close][2212]-> {dataSource-1} closing ...
2025-08-05 09:00:59 [INFO][com.alibaba.druid.pool.DruidDataSource][close][2285]-> {dataSource-1} closed
