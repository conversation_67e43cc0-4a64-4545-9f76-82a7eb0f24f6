2025-08-05 11:50:39 [INFO][org.hibernate.validator.internal.util.Version][<clinit>][21]-> HV000001: Hibernate Validator 8.0.2.Final
2025-08-05 11:50:39 [INFO][com.bilibili.web.BilibiliWebApplication][logStarting][53]-> Starting BilibiliWebApplication using Java 21.0.7 with PID 4008 (D:\Workspace\bilibili-backend\bilibili-web\target\classes started by 26788 in D:\Workspace\bilibili-backend)
2025-08-05 11:50:39 [INFO][com.bilibili.web.BilibiliWebApplication][logStartupProfileInfo][652]-> No active profile set, falling back to 1 default profile: "default"
2025-08-05 11:50:40 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-05 11:50:40 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-08-05 11:50:40 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 10 ms. Found 0 Elasticsearch repository interfaces.
2025-08-05 11:50:40 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-05 11:50:40 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-08-05 11:50:40 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 0 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-08-05 11:50:40 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-05 11:50:40 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-05 11:50:40 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 1 ms. Found 0 Redis repository interfaces.
2025-08-05 11:50:40 [INFO][org.springframework.boot.web.embedded.tomcat.TomcatWebServer][initialize][111]-> Tomcat initialized with port 7071 (http)
2025-08-05 11:50:40 [INFO][org.apache.coyote.http11.Http11NioProtocol][log][173]-> Initializing ProtocolHandler ["http-nio-7071"]
2025-08-05 11:50:40 [INFO][org.apache.catalina.core.StandardService][log][173]-> Starting service [Tomcat]
2025-08-05 11:50:40 [INFO][org.apache.catalina.core.StandardEngine][log][173]-> Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-08-05 11:50:40 [INFO][org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/web]][log][173]-> Initializing Spring embedded WebApplicationContext
2025-08-05 11:50:40 [INFO][org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext][prepareWebApplicationContext][301]-> Root WebApplicationContext: initialization completed in 1664 ms
2025-08-05 11:50:41 [INFO][com.alibaba.druid.spring.boot3.autoconfigure.DruidDataSourceAutoConfigure][dataSource][66]-> Init DruidDataSource
2025-08-05 11:50:41 [INFO][com.alibaba.druid.pool.DruidDataSource][init][1007]-> {dataSource-1} inited
2025-08-05 11:50:42 [INFO][org.apache.coyote.http11.Http11NioProtocol][log][173]-> Starting ProtocolHandler ["http-nio-7071"]
2025-08-05 11:50:42 [INFO][org.springframework.boot.web.embedded.tomcat.TomcatWebServer][start][243]-> Tomcat started on port 7071 (http) with context path '/web'
2025-08-05 11:50:42 [INFO][com.bilibili.web.BilibiliWebApplication][logStarted][59]-> Started BilibiliWebApplication in 3.791 seconds (process running for 4.487)
2025-08-05 11:50:47 [INFO][org.springframework.boot.web.embedded.tomcat.GracefulShutdown][shutDownGracefully][54]-> Commencing graceful shutdown. Waiting for active requests to complete
2025-08-05 11:50:47 [INFO][org.springframework.boot.web.embedded.tomcat.GracefulShutdown][doShutdown][76]-> Graceful shutdown complete
2025-08-05 11:50:47 [INFO][com.alibaba.druid.pool.DruidDataSource][close][2212]-> {dataSource-1} closing ...
2025-08-05 11:50:47 [INFO][com.alibaba.druid.pool.DruidDataSource][close][2285]-> {dataSource-1} closed
